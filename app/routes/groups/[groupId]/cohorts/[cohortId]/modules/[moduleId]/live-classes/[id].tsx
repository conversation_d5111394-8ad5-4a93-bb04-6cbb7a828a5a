import { use<PERSON>ara<PERSON>, useNavigate } from "react-router";
import { X } from "lucide-react";

import { useLiveClass, useRegisterLiveClass } from "~/lib/api/client-queries";
import type { LiveClass } from "~/lib/api/types";

// Utility functions for date/time formatting
function formatTimeRange(startTime: Date, endTime: Date): string {
  const formatTime = (date: Date) =>
    date.toLocaleString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });

  return `${formatTime(startTime)} - ${formatTime(endTime)}`;
}

function formatEventDate(date: Date): string {
  return date.toLocaleString("en-US", {
    weekday: "short",
    month: "short",
    day: "numeric",
  });
}

function formatDuration(startTime: Date, endTime: Date): string {
  const durationMs = endTime.getTime() - startTime.getTime();
  const durationHours = Math.floor(durationMs / (1000 * 60 * 60));
  const durationMinutes = Math.floor(
    (durationMs % (1000 * 60 * 60)) / (1000 * 60)
  );

  if (durationHours > 0) {
    return `${durationHours} hr${
      durationMinutes > 0 ? ` ${durationMinutes} min` : ""
    }`;
  }
  return `${durationMinutes} min`;
}

interface ModalContainerProps {
  onClose: () => void;
  children: React.ReactNode;
}

function ModalContainer({ onClose, children }: ModalContainerProps) {
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-zinc-900 rounded-lg p-6 max-w-md w-full mx-4">
        {children}
      </div>
    </div>
  );
}

interface EventDetailsModalProps {
  event: LiveClass;
  onClose: () => void;
  onRSVP: (eventId: number) => void;
  isUpcoming: boolean;
  isRegistering: boolean;
}

function EventDetailsModal({
  event,
  onClose,
  onRSVP,
  isUpcoming,
  isRegistering,
}: EventDetailsModalProps) {
  const startTime = new Date(event.startAt);
  const endTime = new Date(event.endsAt);
  const timeString = formatTimeRange(startTime, endTime);
  const dateString = formatEventDate(startTime);
  const durationString = formatDuration(startTime, endTime);

  return (
    <div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
      onClick={onClose}
    >
      <div
        className="bg-zinc-900 rounded-lg p-6 max-w-md w-full mx-4"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">Join Event</h2>
          <button
            onClick={onClose}
            className="text-zinc-400 hover:text-white transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <h3 className="text-2xl font-semibold text-white mb-6">
          {event.topic}
        </h3>

        <div className="space-y-4 mb-6">
          <div className="flex justify-between">
            <span className="text-zinc-400">Time</span>
            <span className="text-white">{timeString}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-zinc-400">Date</span>
            <span className="text-white">{dateString}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-zinc-400">Duration</span>
            <span className="text-white">{durationString}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-zinc-400">Host by</span>
            <span className="text-white">Slo Studio</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-zinc-400">Participants</span>
            <div className="flex items-center gap-2">
              <div className="flex -space-x-2">
                {[...Array(Math.min(3, event.numOfParticipants))].map(
                  (_, i) => (
                    <div
                      key={i}
                      className="w-6 h-6 rounded-full bg-zinc-600 border-2 border-zinc-900"
                    />
                  )
                )}
              </div>
              <span className="text-white text-sm">
                {event.numOfParticipants} Attending
              </span>
            </div>
          </div>
        </div>

        {isUpcoming && !event.isRegistered && (
          <button
            onClick={() => onRSVP(event.id)}
            disabled={isRegistering}
            className="w-full bg-indigo-600 hover:bg-indigo-700 disabled:bg-indigo-600/50 text-white font-medium py-3 rounded-lg transition-colors disabled:cursor-not-allowed"
          >
            {isRegistering ? "Registering..." : "RSVP"}
          </button>
        )}
        {event.isRegistered && (
          <div className="text-center text-green-400 font-medium">
            ✓ You're registered for this event
          </div>
        )}
        {!isUpcoming && !event.isRegistered && (
          <div className="text-center text-zinc-400">
            This event has already passed
          </div>
        )}
      </div>
    </div>
  );
}

function LiveClassPage() {
  const params = useParams();
  const navigate = useNavigate();
  const registerLiveClass = useRegisterLiveClass();

  // Extract route parameters
  const { groupId, cohortId, moduleId, liveClassId } = params;

  // Fetch the specific live class data
  const { data: liveClassResponse, isLoading } = useLiveClass(
    groupId!,
    cohortId!,
    moduleId!,
    liveClassId!
  );

  const handleClose = () => {
    navigate(-1);
  };

  const handleRSVP = async (eventId: number) => {
    try {
      await registerLiveClass.mutateAsync({
        groupId: groupId!,
        cohortId: cohortId!,
        moduleId: moduleId!,
        liveClassId: eventId.toString(),
      });
      // Stay on the modal after successful registration to show updated state
    } catch (error) {
      console.error("Failed to register for event:", error);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <ModalContainer onClose={handleClose}>
        <div className="text-center text-white">Loading...</div>
      </ModalContainer>
    );
  }

  // Show error state if no live class found
  if (!liveClassResponse?.success || !liveClassResponse?.data) {
    return (
      <ModalContainer onClose={handleClose}>
        <div className="text-center text-white">Event not found</div>
        <button
          onClick={handleClose}
          className="mt-4 w-full bg-zinc-700 hover:bg-zinc-600 text-white font-medium py-2 rounded-lg transition-colors"
        >
          Close
        </button>
      </ModalContainer>
    );
  }

  const liveClass = liveClassResponse.data;

  // Determine if this is an upcoming event
  const now = new Date();
  const startTime = new Date(liveClass.startAt);
  const isUpcoming = startTime > now;

  return (
    <EventDetailsModal
      event={liveClass}
      onClose={handleClose}
      onRSVP={handleRSVP}
      isUpcoming={isUpcoming}
      isRegistering={registerLiveClass.isPending}
    />
  );
}

export default LiveClassPage;
